<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title><PERSON> - Let's Build Something Extraordinary</title>
		<meta name="description" content="Let's Build Something Extraordinary." />
		<meta name="author" content="Lovable" />
		<meta property="og:image" content="/og-image.png" />
	</head>

	<body>
		<div id="root"></div>
		<script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
		<script type="module" src="/src/main.tsx"></script>
		<script type="text/javascript">
			(function (c, l, a, r, i, t, y) {
				c[a] =
					c[a] ||
					function () {
						(c[a].q = c[a].q || []).push(arguments);
					};
				t = l.createElement(r);
				t.async = 1;
				t.src = 'https://www.clarity.ms/tag/' + i;
				y = l.getElementsByTagName(r)[0];
				y.parentNode.insertBefore(t, y);
			})(window, document, 'clarity', 'script', 'qvrdlm6w3j');
		</script>
	</body>
</html>
