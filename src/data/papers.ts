const papers = [
	{
		title:
			'Exploring the Feasibility and Comparative Analysis of Latent Semantic Analysis (LSA), Word Embeddings (Word2Vec and GloVe), and Seq2Seq Models for Automatic Question Paper Generation',
		authors: ['<PERSON>', '<PERSON><PERSON>'],
		publishedDate: 'March 2024',
		publication: 'Springer',
		tags: ['NLP', 'LSA', 'Word2Vec', 'Seq2Seq', 'Question Paper Generation'],
		slug: 'automatic-question-paper-generation',
		link: 'https://link.springer.com/article/10.1007/s11227-053-5667-8',
		image: '/paper/paper-1.webp',
	},
	{
		title:
			'Graph-Based Retrieval-Augmented Generation (GraphRAG) for Scalable Data Processing in Large-Scale Knowledge Graphs and Enterprise Systems Leveraging Blockchain Technology',
		authors: ['<PERSON>', 'Dr. <PERSON><PERSON>'],
		publishedDate: 'April 2024',
		publication: 'IEEE Xplore',
		tags: ['GraphRAG', 'Knowledge Graphs', 'Neo4j', 'Information Retrieval'],
		slug: 'graph-based-rag-scalable-processing',
		link: 'https://ieeexplore.ieee.org/document/5541170',
		image: '/paper/paper-2.webp',
	},
];

export default papers;
